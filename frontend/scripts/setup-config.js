#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔧 LegalEase Frontend Configuration Setup');
console.log('=========================================');

const envTemplate = `# LegalEase Frontend Environment Configuration
# Generated by setup script

# ==============================================
# CONTRACT ADDRESSES
# ==============================================
# Base Sepolia Testnet (Development)
NEXT_PUBLIC_REGISTRY_BASE_SEPOLIA=

# Base Mainnet (Production) 
NEXT_PUBLIC_REGISTRY_BASE=

# ==============================================
# NETWORK RPC URLS
# ==============================================
NEXT_PUBLIC_BASE_SEPOLIA_RPC=https://sepolia.base.org
NEXT_PUBLIC_BASE_RPC=https://mainnet.base.org

# ==============================================
# WALLETCONNECT CONFIGURATION
# ==============================================
# Get your project ID from: https://cloud.walletconnect.com/
NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID=

# ==============================================
# ENVIRONMENT SETTINGS
# ==============================================
NEXT_PUBLIC_NODE_ENV=development
`;

const envLocalPath = path.join(__dirname, '../.env.local');

function setupEnvironment() {
  try {
    if (fs.existsSync(envLocalPath)) {
      console.log('⚠️  .env.local already exists');
      console.log('   Backup will be created as .env.local.backup');
      fs.copyFileSync(envLocalPath, envLocalPath + '.backup');
      console.log('✅ Backup created');
    }

    fs.writeFileSync(envLocalPath, envTemplate);
    console.log('✅ .env.local created successfully');
    
    console.log('\n📋 Next Steps:');
    console.log('1. Get WalletConnect Project ID from: https://cloud.walletconnect.com/');
    console.log('2. Add your project ID to NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID');
    console.log('3. After contract deployment, add contract address to NEXT_PUBLIC_REGISTRY_BASE_SEPOLIA');
    console.log('4. Run: npm run dev');
    
  } catch (error) {
    console.error('❌ Error creating .env.local:', error.message);
    process.exit(1);
  }
}

function updateContractAddress(address, network = 'base-sepolia') {
  try {
    if (!fs.existsSync(envLocalPath)) {
      console.log('❌ .env.local not found. Run setup first.');
      return;
    }

    let envContent = fs.readFileSync(envLocalPath, 'utf8');
    
    const envVar = network === 'base-sepolia' 
      ? 'NEXT_PUBLIC_REGISTRY_BASE_SEPOLIA'
      : 'NEXT_PUBLIC_REGISTRY_BASE';
    
    const regex = new RegExp(`${envVar}=.*`, 'g');
    const replacement = `${envVar}=${address}`;
    
    if (envContent.includes(`${envVar}=`)) {
      envContent = envContent.replace(regex, replacement);
    } else {
      envContent += `\n${replacement}\n`;
    }
    
    fs.writeFileSync(envLocalPath, envContent);
    console.log(`✅ Updated ${envVar}=${address}`);
    
  } catch (error) {
    console.error('❌ Error updating contract address:', error.message);
  }
}

function checkConfiguration() {
  if (!fs.existsSync(envLocalPath)) {
    console.log('❌ .env.local not found');
    return false;
  }

  const envContent = fs.readFileSync(envLocalPath, 'utf8');
  const requiredVars = [
    'NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID',
    'NEXT_PUBLIC_REGISTRY_BASE_SEPOLIA'
  ];

  console.log('\n📊 Configuration Status:');
  
  let allConfigured = true;
  requiredVars.forEach(varName => {
    const match = envContent.match(new RegExp(`${varName}=(.+)`));
    const value = match ? match[1].trim() : '';
    
    if (value && value !== '' && !value.includes('your_') && !value.includes('<')) {
      console.log(`✅ ${varName}: ${value.slice(0, 20)}...`);
    } else {
      console.log(`❌ ${varName}: Not configured`);
      allConfigured = false;
    }
  });

  if (allConfigured) {
    console.log('\n🎉 All required configuration completed!');
    console.log('   Ready to run: npm run dev');
  } else {
    console.log('\n⚠️  Some configuration still needed');
  }

  return allConfigured;
}

// Handle command line arguments
const command = process.argv[2];
const argument = process.argv[3];

switch (command) {
  case 'setup':
    setupEnvironment();
    break;
    
  case 'update-contract':
    if (!argument) {
      console.log('❌ Usage: node setup-config.js update-contract <CONTRACT_ADDRESS>');
      process.exit(1);
    }
    updateContractAddress(argument);
    break;
    
  case 'check':
    checkConfiguration();
    break;
    
  default:
    console.log('📖 Usage:');
    console.log('  node setup-config.js setup              - Create .env.local template');
    console.log('  node setup-config.js update-contract <addr> - Update contract address');
    console.log('  node setup-config.js check              - Check configuration status');
    console.log('');
    console.log('💡 Quick start:');
    console.log('  1. node setup-config.js setup');
    console.log('  2. Edit .env.local with your WalletConnect project ID');
    console.log('  3. After deployment: node setup-config.js update-contract <address>');
    console.log('  4. node setup-config.js check');
    break;
} 