import base64

# Full base64 string from Postman
base64_string = "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"
# Decode and write to file
with open("image.png", "wb") as f:
    f.write(base64.b64decode(base64_string))
